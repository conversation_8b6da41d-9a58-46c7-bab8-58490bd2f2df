// Background service worker for Perplexity Web MCP Bridge

class McpExtensionBackground {
  constructor() {
    this.isInstalled = false;
    this.ws = null; // WebSocket instance
    this.settings = {}; // Initialize settings object
    this.currentReconnectAttempts = 0;
    this.mcpBridgeServers = []; // Stores server list from McpBridge
    this.isConnecting = false; // Track if any connection attempt is in progress (including reconnect delays)
    this.pingInterval = null; // Ping interval timer
    this.lastPongReceived = null; // Track last pong response
    this.pingTimeout = null; // Ping timeout timer
    this.init();
  }

  getDefaultSettings() {
    return {
      bridgeEnabled: true,
      autoConnect: true,
      bridgeUrl: 'ws://localhost:54319',
      alwaysInject: false,
      reconnectAttempts: 5,
      connectionTimeout: 5000,
      autoExecute: true,
      executionTimeout: 30000,
      // autoDiscoverServers: true, // Commented out
      serverSettings: {},
      showStatusPanel: true,
      panelPosition: 'bottom-left',
      // showToolResults: true, // Commented out
      // resultStyle: 'inline', // Commented out
      verboseLogging: false // Merged debug and verbose
    };
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['mcpSettings'], (result) => {
        const loadedSettings = result.mcpSettings || {};
        this.settings = { ...this.getDefaultSettings(), ...loadedSettings };
        if (this.settings.debugLogging) {
          console.log('[MCP Background] Settings loaded:', this.settings);
        }
        resolve();
      });
    });
  }

  connectWebSocket() {
    if (!this.settings.bridgeEnabled || !this.settings.bridgeUrl) {
      if (this.settings.debugLogging) {
        console.log('[MCP Background] Bridge is disabled or URL not set. Not connecting.');
      }
      this.isConnecting = false;
      this.updateActionBadge();
      this.broadcastStatusUpdate();
      return;
    }
    if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
      if (this.settings.debugLogging) {
        console.log('[MCP Background] WebSocket already open or connecting.');
      }
      return;
    }

    console.log(`[MCP Background] Attempting to connect to WebSocket: ${this.settings.bridgeUrl}`);
    try {
      this.ws = new WebSocket(this.settings.bridgeUrl);
      this.isConnecting = true;
      this.broadcastStatusUpdate();
    } catch (error) {
      console.error('[MCP Background] WebSocket instantiation error:', error);
      this.ws = null;
      this.isConnecting = false;
      this.broadcastStatusUpdate();
      this.handleWebSocketClose();
      return;
    }

    this.ws.onopen = () => {
      console.log('[MCP Background] WebSocket connected to', this.settings.bridgeUrl);
      this.currentReconnectAttempts = 0;
      this.isConnecting = false;
      
      // Start ping/keepalive mechanism
      this.startPingKeepalive();
      
      // Immediately request server list upon connection
      setTimeout(() => {
        this.requestServerListFromBridge();
      }, 500); // Small delay to ensure bridge is ready
      
      this.broadcastStatusUpdate();
    };

    this.ws.onmessage = (event) => {
      if (this.settings.debugLogging) {
        console.log('[MCP Background] Message from WebSocket:', event.data);
      }
      try {
        const message = JSON.parse(event.data);
        if (this.settings.debugLogging) {
          console.log('[MCP Background] Parsed WebSocket message:', message);
        }

        // Handle bridge.js message types
        switch (message.type) {
          case 'connection_established':
            // Bridge sends server list on connection
            this.mcpBridgeServers = message.servers || [];
            if (this.settings.debugLogging) {
              console.log('[MCP Background] Connection established, received server list:', this.mcpBridgeServers);
            }
            this.fetchAllServerTools().then(() => {
              this.broadcastToContentScripts({ type: 'mcp_server_list_update', servers: this.mcpBridgeServers });
              this.broadcastStatusUpdate();
            });
            break;

          case 'servers_list':
            // Response to list_servers request
            this.mcpBridgeServers = message.servers || [];
            if (this.settings.debugLogging) {
              console.log('[MCP Background] Updated server list:', this.mcpBridgeServers);
            }
            // Always fetch tools for all servers when we get a fresh server list
            this.fetchAllServerTools().then(() => {
              this.broadcastToContentScripts({ type: 'mcp_server_list_update', servers: this.mcpBridgeServers });
              this.broadcastStatusUpdate();
              
              // Send updated status to popup if needed
              chrome.runtime.sendMessage({
                type: 'server_data_updated',
                servers: this.mcpBridgeServers
              }).catch(() => {
                // Ignore if popup is not open
              });
            });
            break;

          case 'tools_list':
            // Response to get_tools request
            if (this.settings.debugLogging) {
              console.log('[MCP Background] Received tools list for server:', message.server);
            }
            // Update the specific server's tools
            const server = this.mcpBridgeServers.find(s => s.id === message.server);
            if (server) {
              server.tools = message.tools || [];
              this.broadcastToContentScripts({ type: 'mcp_server_list_update', servers: this.mcpBridgeServers });
              this.broadcastStatusUpdate();
            }
            break;

          case 'tool_result':
            // Response to tools/call request
            if (this.settings.debugLogging) {
              console.log('[MCP Background] Received tool result:', message);
            }
            // Forward to content script as mcp_response for compatibility
            this.broadcastToContentScripts({
              type: 'mcp_message',
              data: {
                type: 'mcp_response',
                id: message.id,
                result: message.result,
                server: message.server,
                tool: message.tool
              }
            });
            break;

          case 'error':
            // Error response
            if (this.settings.debugLogging) {
              console.log('[MCP Background] Received error:', message);
            }
            // Forward to content script as mcp_response with error
            this.broadcastToContentScripts({
              type: 'mcp_message',
              data: {
                type: 'mcp_response',
                id: message.id,
                error: message.error
              }
            });
            break;

          case 'pong':
            // Response to ping
            this.lastPongReceived = Date.now();
            if (this.pingTimeout) {
              clearTimeout(this.pingTimeout);
              this.pingTimeout = null;
            }
            if (this.settings.debugLogging) {
              console.log('[MCP Background] Received pong');
            }
            break;

          default:
            if (this.settings.debugLogging) {
              console.log('[MCP Background] Unknown message type:', message.type);
            }
            // Forward unknown messages to content scripts
            this.broadcastToContentScripts({ type: 'mcp_message', data: message });
        }
      } catch (e) {
        console.error('[MCP Background] Error parsing WebSocket message:', e);
      }
    };

    this.ws.onerror = (error) => {
      console.error('[MCP Background] WebSocket error:', error);
    };

    this.ws.onclose = (event) => {
      console.log(`[MCP Background] WebSocket disconnected. Code: ${event.code}, Reason: ${event.reason ? event.reason : 'N/A'}, WasClean: ${event.wasClean}`);
      this.isConnecting = false;
      this.stopPingKeepalive();
      this.broadcastStatusUpdate();
      this.handleWebSocketClose();
    };
  }

  handleWebSocketClose() {
    this.ws = null;
    // During reconnect delay, isConnecting should be false.
    const maxReconnectAttempts = this.settings.reconnectAttempts === -1 ? Infinity : this.settings.reconnectAttempts;
    if (this.settings.bridgeEnabled && this.settings.autoConnect && this.currentReconnectAttempts < maxReconnectAttempts) {
      this.currentReconnectAttempts++;
      this.isConnecting = false;
      this.broadcastStatusUpdate();
      const connectionTimeout = this.settings.connectionTimeout === -1 ? 5000 : this.settings.connectionTimeout;
      const delay = Math.min(30000, (Math.pow(1.5, this.currentReconnectAttempts) * 1000) + (connectionTimeout / 5));
      console.log(`[MCP Background] Reconnecting attempt ${this.currentReconnectAttempts}/${maxReconnectAttempts === Infinity ? '∞' : maxReconnectAttempts} in ${delay / 1000}s...`);
      setTimeout(() => this.connectWebSocket(), delay);
    } else if (this.settings.bridgeEnabled && this.settings.autoConnect) {
      this.isConnecting = false;
      this.broadcastStatusUpdate();
      console.log('[MCP Background] Max reconnection attempts reached or autoConnect disabled during retries.');
    } else {
      this.isConnecting = false;
      this.broadcastStatusUpdate();
    }
  }

  disconnectWebSocket() {
    if (this.ws) {
      console.log('[MCP Background] Disconnecting WebSocket.');
      this.ws.onclose = null;
      this.ws.close();
      this.ws = null;
    }
    this.stopPingKeepalive();
    this.broadcastStatusUpdate();
  }

  sendWebSocketMessage(messageObject) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        const messageString = JSON.stringify(messageObject);
        this.ws.send(messageString);
        if (this.settings.debugLogging) {
          console.log('[MCP Background] Sent WebSocket message:', messageObject);
        }
      } catch (e) {
        console.error('[MCP Background] Error sending WebSocket message:', e);
      }
    } else {
      console.warn('[MCP Background] WebSocket not open. Message not sent:', messageObject);
    }
  }

  broadcastStatusUpdate() { // Will be modified by diff for serverCount
    const status = {
      type: 'bridge_status_update',
      isConnected: !!(this.ws && this.ws.readyState === WebSocket.OPEN),
      isConnecting: this.isConnecting, // Use our own flag to cover reconnect delays
      bridgeUrl: this.settings.bridgeUrl,
      serverCount: (this.mcpBridgeServers && Array.isArray(this.mcpBridgeServers)) ? this.mcpBridgeServers.length : 0,
      maxReconnectAttemptsReached: !!(
        this.settings.bridgeEnabled && // Only relevant if bridge is supposed to be enabled
        this.settings.autoConnect &&    // And if it's supposed to auto-connect
        (!this.ws || (this.ws.readyState !== WebSocket.OPEN && this.ws.readyState !== WebSocket.CONNECTING)) && // And it's not currently open or trying to connect
        this.currentReconnectAttempts >= this.settings.reconnectAttempts // And attempts are exhausted
      )
    };
    chrome.runtime.sendMessage(status).catch(e => { /* Ignore */ });
    this.updateActionBadge();
  }

  updateActionBadge() {
    if (!chrome.action) return;

    if (this.settings.bridgeEnabled && this.ws && this.ws.readyState === WebSocket.OPEN) {
      chrome.action.setBadgeText({ text: 'ON' });
      chrome.action.setBadgeBackgroundColor({ color: '#28a745' });
    } else if (this.settings.bridgeEnabled && this.ws && this.ws.readyState === WebSocket.CONNECTING) {
      chrome.action.setBadgeText({ text: '...' });
      chrome.action.setBadgeBackgroundColor({ color: '#ffc107' });
    }
    else {
      chrome.action.setBadgeText({ text: 'OFF' });
      chrome.action.setBadgeBackgroundColor({ color: '#dc3545' });
    }
  }

  broadcastToContentScripts(message) {
    chrome.tabs.query({ url: "*://*.perplexity.ai/*" }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.warn("[MCP Background] Error querying tabs:", chrome.runtime.lastError.message);
        return;
      }
      tabs.forEach(tab => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, message).catch(e => {
            if (this.settings.debugLogging) {
              console.warn(`[MCP Background] Error sending message to tab ${tab.id}:`, e.message);
            }
          });
        }
      });
    });
  }

  async init() {
    console.log('[MCP Background] Initializing...');
    await this.loadSettings();

    if (this.settings.autoConnect && this.settings.bridgeEnabled) {
      this.connectWebSocket();
    }
    this.updateActionBadge();

    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstall(details);
    });

    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'sync' && changes.mcpSettings) {
        const newSettingsSource = changes.mcpSettings.newValue || {};
        const oldSettingsSource = changes.mcpSettings.oldValue || {};

        const newSettings = { ...this.getDefaultSettings(), ...newSettingsSource };
        const oldSettings = { ...this.getDefaultSettings(), ...oldSettingsSource };

        const bridgeUrlChanged = newSettings.bridgeUrl !== oldSettings.bridgeUrl;
        const bridgeEnabledChanged = newSettings.bridgeEnabled !== oldSettings.bridgeEnabled;
        const autoConnectChanged = newSettings.autoConnect !== oldSettings.autoConnect;
        const reconnectAttemptsChanged = newSettings.reconnectAttempts !== oldSettings.reconnectAttempts;

        this.settings = newSettings;
        if (this.settings.debugLogging) {
          console.log('[MCP Background] Settings updated:', this.settings);
        }

        let needsReconnect = false;
        if (bridgeEnabledChanged) {
          if (this.settings.bridgeEnabled && this.settings.autoConnect) {
            needsReconnect = true;
          } else if (!this.settings.bridgeEnabled) {
            this.disconnectWebSocket();
          }
        } else if (this.settings.bridgeEnabled && (bridgeUrlChanged || autoConnectChanged)) {
          needsReconnect = true;
        }

        if (needsReconnect) {
          this.disconnectWebSocket();
          if (this.settings.autoConnect) {
            this.currentReconnectAttempts = 0;
            this.connectWebSocket();
          }
        } else if (reconnectAttemptsChanged && this.settings.bridgeEnabled && this.settings.autoConnect && (!this.ws || this.ws.readyState === WebSocket.CLOSED)) {
          this.currentReconnectAttempts = 0;
          this.connectWebSocket();
        }
        this.updateActionBadge();
      }
    });

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });

    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });
  }

  handleInstall(details) {
    if (details.reason === 'install') {
      console.log('[MCP Background] Extension installed');
    } else if (details.reason === 'update') {
      console.log('[MCP Background] Extension updated');
    }
    this.isInstalled = true;
  }

  async handleMessage(message, sender, sendResponse) {
    if (this.settings.debugLogging) {
      console.log('[MCP Background] Received message:', message, 'from:', sender.tab ? sender.tab.url : "extension");
    }
    try {
      switch (message.type) {
        case 'get_status':
          sendResponse({
            status: 'ok',
            bridge_connected: !!(this.ws && this.ws.readyState === WebSocket.OPEN),
            bridge_connecting: this.isConnecting,
            bridge_url: this.settings.bridgeUrl,
            installed: this.isInstalled,
            settings: this.settings,
            mcp_servers: this.mcpBridgeServers,
            maxReconnectAttemptsReached: !!(
              this.settings.bridgeEnabled &&
              this.settings.autoConnect &&
              (!this.ws || (this.ws.readyState !== WebSocket.OPEN && this.ws.readyState !== WebSocket.CONNECTING)) &&
              this.currentReconnectAttempts >= this.settings.reconnectAttempts
            )
          });
          break;

        case 'mcp_request':
          if (message.payload) {
            // Validate and wrap the payload for bridge.js protocol
            const { serverId, request } = message.payload;
            if (!serverId || !request || !request.method) {
              console.error('[MCP Background] Invalid MCP request payload:', message.payload);
              sendResponse({ error: 'Invalid MCP request payload' });
              break;
            }
            // Attach server id to the request if needed
            let wsMessage = null;
            if (request.method === 'tools/call') {
              wsMessage = {
                type: 'tools/call',
                server: serverId,
                tool: request.params.name,
                arguments: request.params.arguments,
                id: request.id
              };
            } else if (request.method === 'tools/list') {
              wsMessage = {
                type: 'get_tools',
                server: serverId,
                id: request.id
              };
            } else {
              wsMessage = { ...request, server: serverId, id: request.id };
            }
            this.sendWebSocketMessage(wsMessage);
            sendResponse({ success: true });
          } else {
            console.error('[MCP Background] No payload in mcp_request');
            sendResponse({ error: 'No payload in mcp_request' });
          }
          break;

        case 'bridge_test':
          const healthData = await this.checkBridgeHealth();
          sendResponse({
            success: this.ws && this.ws.readyState === WebSocket.OPEN,
            websocket_state: this.ws ? this.ws.readyState : 'NONE',
            bridge_url: this.settings.bridgeUrl,
            health_status: healthData ? healthData.status : 'unknown',
            servers: healthData ? healthData.servers : [],
            clients: healthData ? healthData.clients : 0,
            error: healthData ? null : "Health check failed or bridge not reachable"
          });
          break;

        case 'open_options':
          chrome.runtime.openOptionsPage();
          sendResponse({ success: true });
          break;

        case 'get_servers':
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            // Always request fresh server list from bridge
            this.requestServerListFromBridge();
            
            // Wait for response before sending back
            setTimeout(() => {
              sendResponse({ success: true, servers: this.mcpBridgeServers || [] });
            }, 1500); // Increased timeout to ensure we get fresh data
          } else {
            // Fallback to health check if WebSocket isn't connected
            const healthData = await this.checkBridgeHealth();
            if (healthData && healthData.servers) {
              // Health check returns server objects with more detailed info
              const formattedServers = healthData.servers.map(serverInfo => ({
                id: serverInfo.name,
                name: serverInfo.name,
                type: serverInfo.type || 'stdio',
                status: serverInfo.status === 'connected' ? 'connected' : 'disconnected',
                tools: [], // Tools will be fetched separately
                toolCount: serverInfo.tools || 0
              }));
              sendResponse({ success: true, servers: formattedServers });
            } else {
              sendResponse({ success: false, error: 'Bridge not connected and health check failed.', servers: [] });
            }
          }
          break;

        case 'connect_bridge':
          this.currentReconnectAttempts = 0;
          this.connectWebSocket();
          // Request fresh server data after connection attempt
          setTimeout(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
              this.requestServerListFromBridge();
            }
          }, 1000);
          sendResponse({ success: true, message: 'Connection attempt initiated.' });
          break;

        case 'disconnect_bridge':
          this.disconnectWebSocket();
          sendResponse({ success: true, message: 'Disconnection attempt initiated.' });
          break;

        case 'open_tab':
          // Handle tab opening from content script
          if (message.url) {
            chrome.tabs.create({ url: message.url });
            sendResponse({ success: true });
          } else {
            sendResponse({ error: 'No URL provided' });
          }
          break;
        
        case 'save_thread_state':
          if (message.threadId && message.state) {
            const key = `mcp_thread_${message.threadId}`;
            await chrome.storage.local.set({ [key]: message.state });
            if (this.settings.debugLogging) console.log(`[MCP Background] Saved state for thread: ${message.threadId}`);
            sendResponse({ success: true });
          } else {
            sendResponse({ success: false, error: 'Missing threadId or state' });
          }
          break;

        case 'load_thread_state':
          if (message.threadId) {
            const key = `mcp_thread_${message.threadId}`;
            const result = await chrome.storage.local.get([key]);
            if (this.settings.verboseLogging) console.log(`[MCP Background] Loaded state for thread: ${message.threadId}`, result[key] ? 'found' : 'not found');
            sendResponse(result[key] || null);
          } else {
            sendResponse(null);
          }
          break;
        
        case 'export_thread_data':
            const allLocalStorage = await chrome.storage.local.get(null);
            const threadData = {};
            for (const key in allLocalStorage) {
                if (key.startsWith('mcp_thread_')) {
                    threadData[key] = JSON.parse(allLocalStorage[key]);
                }
            }
            sendResponse({success: true, data: threadData});
            break;

        case 'import_thread_data':
            if (message.data) {
                const dataToStore = {};
                for (const key in message.data) {
                    if (key.startsWith('mcp_thread_')) {
                        dataToStore[key] = JSON.stringify(message.data[key]);
                    }
                }
                await chrome.storage.local.set(dataToStore);
                sendResponse({success: true, count: Object.keys(dataToStore).length});
            } else {
                sendResponse({success: false, error: 'No data provided for import'});
            }
            break;

        case 'reset_thread_data':
            const allItems = await chrome.storage.local.get(null);
            const keysToRemove = [];
            for (const key in allItems) {
                if (key.startsWith('mcp_thread_')) {
                    keysToRemove.push(key);
                }
            }
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
            }
            sendResponse({success: true, count: keysToRemove.length});
            break;

        default:
          if (this.settings.verboseLogging) {
            console.log('[MCP Background] Unknown message type:', message.type);
          }
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('[MCP Background] Message handling error:', error.message, error.stack);
      sendResponse({ error: error.message });
    }
  }

  handleTabUpdate(tabId, changeInfo, tab) {
    if (changeInfo.status === 'complete' && tab.url && (tab.url.includes('perplexity.ai'))) {
      if (this.settings.debugLogging) {
        console.log('[MCP Background] Perplexity page loaded/updated:', tab.url);
      }
      this.ensureContentScriptInjected(tabId);
    }

    // Monitor URL changes for thread management
    if (changeInfo.url && tab.url && tab.url.includes('perplexity.ai')) {
      if (this.settings.debugLogging) {
        console.log('[MCP Background] URL changed to:', tab.url);
      }
      
      // Send URL change notification to content script
      chrome.tabs.sendMessage(tabId, {
        type: 'url_changed',
        url: tab.url,
        tabId: tabId
      }).catch(e => {
        if (this.settings.debugLogging) {
          console.warn('[MCP Background] Could not send URL change message:', e.message);
        }
      });
    }
  }

  async ensureContentScriptInjected(tabId) {
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => !!window.mcpClient
      });

      if (!results || !results[0] || !results[0].result) {
        if (this.settings.debugLogging) {
          console.log('[MCP Background] Injecting content script into tab:', tabId);
        }
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['js/content.js']
        });
      }
    } catch (error) {
      if (error.message.includes("Cannot access contents of url") || error.message.includes("Extension context invalidated")) {
        // Common, harmless errors for internal pages or during extension reload
      } else if (this.settings.debugLogging) {
        console.error('[MCP Background] Failed to inject/check content script:', error.message);
      }
    }
  }

  async checkBridgeHealth() {
    if (!this.settings.bridgeEnabled) return null;
    try {
      // Bridge HTTP server runs on port 54320 (WebSocket port + 1)
      const healthUrl = `http://localhost:${(parseInt(this.settings.bridgeUrl.split(':').pop()) || 54319) + 1}/health`;
      if (this.settings.debugLogging) console.log('[MCP Background] Checking bridge health at', healthUrl);

      const controller = new AbortController();
      const connectionTimeout = this.settings.connectionTimeout === -1 ? 30000 : this.settings.connectionTimeout;
      const timeoutId = setTimeout(() => controller.abort(), connectionTimeout);

      const response = await fetch(healthUrl, { signal: controller.signal });
      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        if (this.settings.debugLogging) console.log('[MCP Background] Bridge health data:', data);
        return data;
      } else {
        if (this.settings.debugLogging) console.warn('[MCP Background] Bridge health check non-OK response:', response.status);
      }
    } catch (error) {
      if (this.settings.debugLogging) console.warn('[MCP Background] Bridge health check failed:', error.name === 'AbortError' ? 'Timeout' : error.message);
    }
    return null;
  }
  // Request server list from bridge
  requestServerListFromBridge() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const requestId = 'server_list_' + Date.now();
      this.sendWebSocketMessage({
        type: 'list_servers',
        id: requestId
      });
      if (this.settings.debugLogging) {
        console.log('[MCP Background] Requested server list from bridge');
      }
    }
  }

  // Fetch tools for all servers and attach to mcpBridgeServers
  async fetchAllServerTools() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN || !Array.isArray(this.mcpBridgeServers)) return;
    const fetchToolsForServer = (server) => {
      return new Promise((resolve) => {
        const reqId = 'tools_' + (server.id || server.name || Math.random());
        const handler = (event) => {
          try {
            const msg = JSON.parse(event.data);
            if ((msg.type === 'tools_list' || msg.type === 'tools') && msg.server === server.id) {
              // bridge.js sends tools_list with a tools array
              server.tools = Array.isArray(msg.tools) ? msg.tools : (Array.isArray(msg.tools_list) ? msg.tools_list : []);
              if (!server.tools.length && Array.isArray(msg.tools)) {
                server.tools = msg.tools;
              } else if (!server.tools.length && Array.isArray(msg.tools_list)) {
                server.tools = msg.tools_list;
              }
              this.ws.removeEventListener('message', handler);
              resolve();
            }
          } catch { }
        };
        this.ws.addEventListener('message', handler);
        // Send request for tools for this server
        this.sendWebSocketMessage({ type: 'get_tools', server: server.id });
        // Timeout fallback
        setTimeout(() => {
          this.ws.removeEventListener('message', handler);
          if (!server.tools) server.tools = [];
          resolve();
        }, 2000);
      });
    };
    await Promise.all(this.mcpBridgeServers.map(fetchToolsForServer));
  }

  // Start ping/keepalive mechanism
  startPingKeepalive() {
    this.stopPingKeepalive(); // Clear any existing intervals
    
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    this.lastPongReceived = Date.now();
    
    // Send ping every 20 seconds
    this.pingInterval = setInterval(() => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        this.stopPingKeepalive();
        return;
      }

      // Check if we missed a pong (60 second timeout)
      const timeSinceLastPong = Date.now() - (this.lastPongReceived || 0);
      if (timeSinceLastPong > 60000) {
        console.warn('[MCP Background] Ping timeout - no pong received in 60s, closing connection');
        this.ws.close(1000, 'Ping timeout');
        return;
      }

      // Send ping
      this.sendWebSocketMessage({ type: 'ping', timestamp: Date.now() });
      
      if (this.settings.debugLogging) {
        console.log('[MCP Background] Sent ping');
      }

      // Set timeout to detect missing pong
      this.pingTimeout = setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          console.warn('[MCP Background] Pong timeout - closing connection');
          this.ws.close(1000, 'Pong timeout');
        }
      }, 15000); // 15 second pong timeout
      
    }, 20000); // Send ping every 20 seconds
  }

  // Stop ping/keepalive mechanism
  stopPingKeepalive() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    if (this.pingTimeout) {
      clearTimeout(this.pingTimeout);
      this.pingTimeout = null;
    }
    this.lastPongReceived = null;
  }
}

const mcpBackground = new McpExtensionBackground();
console.log('[MCP Background] Service worker loaded.');